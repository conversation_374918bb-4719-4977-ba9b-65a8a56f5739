<?php
/**
 * Archive template for pool products
 */

get_header(); ?>

<div class="hero-section products-hero">
    <div class="hero-background">
        <img src="<?php echo get_template_directory_uri(); ?>/images/pool-water-ripples.jpg" alt="Pool Water" class="hero-bg-image">
        <div class="hero-overlay"></div>
        <div class="water-particles"></div>
        <div class="sun-flare"></div>
    </div>
    <div class="hero-content">
        <div class="container">
            <h1 class="hero-title">EZ Pool Products</h1>
            <p class="hero-subtitle">Premium Made-to-Order Pools - Built in the USA</p>
        </div>
    </div>
</div>

<div class="products-section">
    <div class="container">
        <!-- Product Filters -->
        <div class="product-filters">
            <div class="filter-group">
                <label for="category-filter">Pool Category:</label>
                <select id="category-filter" class="filter-select">
                    <option value="">All Categories</option>
                    <option value="7-wide-lap">7' Wide Lap Pools</option>
                    <option value="12-wide-family">12' Wide Family Pools</option>
                    <option value="17-wide-super">17' Wide Super Pools</option>
                    <option value="22-wide-giant">22' Wide Giant Pools</option>
                </select>
            </div>
            
            <div class="filter-group">
                <label for="size-filter">Pool Size:</label>
                <select id="size-filter" class="filter-select">
                    <option value="">All Sizes</option>
                    <option value="small">Small (7x12 - 12x17)</option>
                    <option value="medium">Medium (12x22 - 17x27)</option>
                    <option value="large">Large (17x32 - 22x47)</option>
                    <option value="extra-large">Extra Large (22x52+)</option>
                </select>
            </div>
            
            <div class="filter-group">
                <label for="price-filter">Price Range:</label>
                <select id="price-filter" class="filter-select">
                    <option value="">All Prices</option>
                    <option value="under-2000">Under $2,000</option>
                    <option value="2000-4000">$2,000 - $4,000</option>
                    <option value="4000-6000">$4,000 - $6,000</option>
                    <option value="over-6000">Over $6,000</option>
                </select>
            </div>
            
            <div class="filter-group">
                <button id="clear-filters" class="btn btn-secondary">Clear Filters</button>
            </div>
        </div>

        <!-- Product Grid -->
        <div class="products-grid" id="products-grid">
            <?php if (have_posts()) : ?>
                <?php while (have_posts()) : the_post(); 
                    $width = get_post_meta(get_the_ID(), '_pool_width', true);
                    $length_min = get_post_meta(get_the_ID(), '_pool_length_min', true);
                    $length_max = get_post_meta(get_the_ID(), '_pool_length_max', true);
                    $base_price = get_post_meta(get_the_ID(), '_pool_base_price', true);
                    $shipping_cost = get_post_meta(get_the_ID(), '_pool_shipping_cost', true);
                    $price_range = get_post_meta(get_the_ID(), '_pool_price_range', true);
                    $gallons = get_post_meta(get_the_ID(), '_pool_gallons', true);
                    
                    // Get category from post terms
                    $categories = get_the_terms(get_the_ID(), 'pool_category');
                    $category_class = '';
                    if ($categories && !is_wp_error($categories)) {
                        $category_class = $categories[0]->slug;
                    }
                    
                    // Determine size class
                    $size_class = 'small';
                    if ($width >= 17 && $length_max >= 32) {
                        $size_class = 'large';
                    } elseif ($width >= 12 && $length_max >= 22) {
                        $size_class = 'medium';
                    }
                    
                    // Determine price class
                    $price_class = 'under-2000';
                    if ($base_price >= 6000) {
                        $price_class = 'over-6000';
                    } elseif ($base_price >= 4000) {
                        $price_class = '4000-6000';
                    } elseif ($base_price >= 2000) {
                        $price_class = '2000-4000';
                    }
                ?>
                
                <div class="product-card" data-category="<?php echo esc_attr($category_class); ?>" data-size="<?php echo esc_attr($size_class); ?>" data-price="<?php echo esc_attr($price_class); ?>">
                    <div class="product-image">
                        <?php if (has_post_thumbnail()) : ?>
                            <a href="<?php the_permalink(); ?>">
                                <?php the_post_thumbnail('medium', array('class' => 'product-img')); ?>
                            </a>
                        <?php else : ?>
                            <a href="<?php the_permalink(); ?>">
                                <img src="<?php echo get_template_directory_uri(); ?>/images/pool-water-ripples.jpg" alt="<?php the_title(); ?>" class="product-img">
                            </a>
                        <?php endif; ?>
                        <div class="product-overlay">
                            <a href="<?php the_permalink(); ?>" class="btn btn-primary">View Details</a>
                        </div>
                    </div>
                    
                    <div class="product-info">
                        <h3 class="product-title">
                            <a href="<?php the_permalink(); ?>"><?php the_title(); ?></a>
                        </h3>
                        
                        <div class="product-specs">
                            <?php if ($width && $length_min) : ?>
                                <div class="spec-item">
                                    <span class="spec-label">Size:</span>
                                    <span class="spec-value">
                                        <?php echo esc_html($width); ?>' x <?php echo esc_html($length_min); ?>'
                                        <?php if ($length_max && $length_max != $length_min) : ?>
                                            - <?php echo esc_html($length_max); ?>'
                                        <?php endif; ?>
                                    </span>
                                </div>
                            <?php endif; ?>
                            
                            <?php if ($gallons) : ?>
                                <div class="spec-item">
                                    <span class="spec-label">Capacity:</span>
                                    <span class="spec-value"><?php echo number_format($gallons); ?> gallons</span>
                                </div>
                            <?php endif; ?>
                        </div>
                        
                        <div class="product-pricing">
                            <?php if ($price_range) : ?>
                                <div class="price-range"><?php echo esc_html($price_range); ?></div>
                            <?php elseif ($base_price) : ?>
                                <div class="base-price">Starting at $<?php echo number_format($base_price); ?></div>
                                <?php if ($shipping_cost) : ?>
                                    <div class="shipping-note">+ $<?php echo number_format($shipping_cost); ?> shipping</div>
                                <?php endif; ?>
                            <?php endif; ?>
                        </div>
                        
                        <div class="product-excerpt">
                            <?php echo wp_trim_words(get_the_excerpt(), 20, '...'); ?>
                        </div>
                        
                        <div class="product-actions">
                            <a href="<?php the_permalink(); ?>" class="btn btn-primary">Learn More</a>
                            <a href="<?php the_permalink(); ?>#contact" class="btn btn-secondary">Get Quote</a>
                        </div>
                    </div>
                </div>
                
                <?php endwhile; ?>
            <?php else : ?>
                <div class="no-products">
                    <h3>No products found</h3>
                    <p>We're currently updating our product catalog. Please check back soon or contact us for more information.</p>
                    <a href="<?php echo home_url('/contact'); ?>" class="btn btn-primary">Contact Us</a>
                </div>
            <?php endif; ?>
        </div>
        
        <!-- Pagination -->
        <div class="products-pagination">
            <?php
            echo paginate_links(array(
                'prev_text' => '&laquo; Previous',
                'next_text' => 'Next &raquo;',
                'type' => 'list',
                'class' => 'pagination'
            ));
            ?>
        </div>
    </div>
</div>

<!-- Product Information Section -->
<div class="product-info-section">
    <div class="container">
        <div class="info-grid">
            <div class="info-card">
                <div class="info-icon">
                    <i class="fas fa-tools"></i>
                </div>
                <h3>Made-to-Order</h3>
                <p>Each EZ Pool is custom manufactured to your specifications right here in the USA with a 5-year warranty.</p>
            </div>
            
            <div class="info-card">
                <div class="info-icon">
                    <i class="fas fa-shipping-fast"></i>
                </div>
                <h3>Fast Delivery</h3>
                <p>Ships in 10-14 business days. Professional installation available in most areas.</p>
            </div>
            
            <div class="info-card">
                <div class="info-icon">
                    <i class="fas fa-medal"></i>
                </div>
                <h3>Premium Quality</h3>
                <p>Built with only American-made components and backed by our comprehensive warranty.</p>
            </div>
            
            <div class="info-card">
                <div class="info-icon">
                    <i class="fas fa-palette"></i>
                </div>
                <h3>Customizable</h3>
                <p>Choose from multiple colors, sizes, and accessories to create your perfect pool.</p>
            </div>
        </div>
    </div>
</div>

<script>
// Product filtering functionality
document.addEventListener('DOMContentLoaded', function() {
    const categoryFilter = document.getElementById('category-filter');
    const sizeFilter = document.getElementById('size-filter');
    const priceFilter = document.getElementById('price-filter');
    const clearFilters = document.getElementById('clear-filters');
    const productCards = document.querySelectorAll('.product-card');
    
    function filterProducts() {
        const categoryValue = categoryFilter.value;
        const sizeValue = sizeFilter.value;
        const priceValue = priceFilter.value;
        
        productCards.forEach(card => {
            const cardCategory = card.dataset.category;
            const cardSize = card.dataset.size;
            const cardPrice = card.dataset.price;
            
            const categoryMatch = !categoryValue || cardCategory === categoryValue;
            const sizeMatch = !sizeValue || cardSize === sizeValue;
            const priceMatch = !priceValue || cardPrice === priceValue;
            
            if (categoryMatch && sizeMatch && priceMatch) {
                card.style.display = 'block';
                card.style.animation = 'fadeIn 0.3s ease-in';
            } else {
                card.style.display = 'none';
            }
        });
    }
    
    categoryFilter.addEventListener('change', filterProducts);
    sizeFilter.addEventListener('change', filterProducts);
    priceFilter.addEventListener('change', filterProducts);
    
    clearFilters.addEventListener('click', function() {
        categoryFilter.value = '';
        sizeFilter.value = '';
        priceFilter.value = '';
        filterProducts();
    });
});
</script>

<?php get_footer(); ?>
