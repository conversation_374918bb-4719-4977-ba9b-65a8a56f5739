<?php
/**
 * EZ Pools Theme Functions
 */

// Theme setup
function ezpools_theme_setup() {
    // Add theme support for various features
    add_theme_support('title-tag');
    add_theme_support('post-thumbnails');
    add_theme_support('html5', array(
        'search-form',
        'comment-form',
        'comment-list',
        'gallery',
        'caption',
    ));
    add_theme_support('custom-logo');
    add_theme_support('customize-selective-refresh-widgets');
    
    // Register navigation menus
    register_nav_menus(array(
        'primary' => __('Primary Menu', 'ezpools'),
        'footer' => __('Footer Menu', 'ezpools'),
    ));
}
add_action('after_setup_theme', 'ezpools_theme_setup');

// Enqueue styles and scripts with aggressive cache busting
function ezpools_scripts() {
    // Generate aggressive cache-busting timestamp (microseconds for maximum freshness)
    $cache_buster = microtime(true);

    // For development, use file modification time PLUS random number for even better cache busting
    $style_file = get_template_directory() . '/style.css';
    $js_file = get_template_directory() . '/js/theme.js';

    $style_version = file_exists($style_file) ? filemtime($style_file) . '.' . rand(1000, 9999) : $cache_buster;
    $js_version = file_exists($js_file) ? filemtime($js_file) . '.' . rand(1000, 9999) : $cache_buster;

    // Enqueue theme stylesheet with aggressive cache busting
    wp_enqueue_style('ezpools-style', get_stylesheet_uri(), array(), $style_version);

    // Enqueue Google Fonts
    wp_enqueue_style('ezpools-fonts', 'https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&family=Poppins:wght@300;400;500;600;700;800&display=swap', array(), null);

    // Enqueue theme JavaScript with aggressive cache busting
    wp_enqueue_script('ezpools-script', get_template_directory_uri() . '/js/theme.js', array('jquery'), $js_version, true);

    // Smooth scrolling for anchor links (if file exists)
    $smooth_scroll_file = get_template_directory() . '/js/smooth-scroll.js';
    if (file_exists($smooth_scroll_file)) {
        $smooth_version = filemtime($smooth_scroll_file) . '.' . rand(1000, 9999);
        wp_enqueue_script('ezpools-smooth-scroll', get_template_directory_uri() . '/js/smooth-scroll.js', array('jquery'), $smooth_version, true);
    }

    // Enqueue product scripts on product pages
    if (is_post_type_archive('pool_product') || is_singular('pool_product')) {
        $products_js_file = get_template_directory() . '/assets/js/products.js';
        $products_js_version = file_exists($products_js_file) ? filemtime($products_js_file) . '.' . rand(1000, 9999) : $cache_buster;
        wp_enqueue_script('ezpools-products', get_template_directory_uri() . '/assets/js/products.js', array(), $products_js_version, true);
    }
}
add_action('wp_enqueue_scripts', 'ezpools_scripts');

// Disable all caching for development
function ezpools_disable_caching() {
    // Disable WordPress caching
    if (!defined('WP_CACHE')) {
        define('WP_CACHE', false);
    }

    // Send aggressive no-cache headers
    if (!is_admin()) {
        header('Cache-Control: no-cache, no-store, must-revalidate, max-age=0');
        header('Pragma: no-cache');
        header('Expires: Thu, 01 Jan 1970 00:00:00 GMT');
        header('Last-Modified: ' . gmdate('D, d M Y H:i:s') . ' GMT');
        header('ETag: "' . md5(microtime()) . '"');
    }

    // Disable object cache
    wp_suspend_cache_addition(true);
    wp_suspend_cache_invalidation(true);
}
add_action('init', 'ezpools_disable_caching');

// Remove version numbers from CSS and JS (then add our own)
function ezpools_remove_version_scripts_styles($src) {
    if (strpos($src, 'ver=')) {
        $src = remove_query_arg('ver', $src);
        // Add timestamp to force reload
        $src = add_query_arg('v', current_time('timestamp'), $src);
    }
    return $src;
}
add_filter('style_loader_src', 'ezpools_remove_version_scripts_styles', 9999);
add_filter('script_loader_src', 'ezpools_remove_version_scripts_styles', 9999);

// Disable WordPress emoji scripts (reduces HTTP requests)
function ezpools_disable_emojis() {
    remove_action('wp_head', 'print_emoji_detection_script', 7);
    remove_action('admin_print_scripts', 'print_emoji_detection_script');
    remove_action('wp_print_styles', 'print_emoji_styles');
    remove_action('admin_print_styles', 'print_emoji_styles');
    remove_filter('the_content_feed', 'wp_staticize_emoji');
    remove_filter('comment_text_rss', 'wp_staticize_emoji');
    remove_filter('wp_mail', 'wp_staticize_emoji_for_email');
}
add_action('init', 'ezpools_disable_emojis');

// Add no-cache meta tags to head
function ezpools_add_nocache_headers() {
    if (!is_admin()) {
        echo '<meta http-equiv="Cache-Control" content="no-cache, no-store, must-revalidate">' . "\n";
        echo '<meta http-equiv="Pragma" content="no-cache">' . "\n";
        echo '<meta http-equiv="Expires" content="0">' . "\n";
        echo '<meta name="cache-control" content="no-cache">' . "\n";
        echo '<meta name="expires" content="0">' . "\n";
        echo '<meta name="pragma" content="no-cache">' . "\n";
    }
}
add_action('wp_head', 'ezpools_add_nocache_headers', 1);

// Add cache-busting JavaScript to force browser refresh
function ezpools_force_browser_refresh() {
    if (!is_admin()) {
        echo '<script>
        // Force browser to not cache anything
        if (window.performance && window.performance.navigation.type === 1) {
            // Page was refreshed, clear any cached resources
            if ("caches" in window) {
                caches.keys().then(function(names) {
                    names.forEach(function(name) {
                        caches.delete(name);
                    });
                });
            }
        }

        // Disable browser caching for AJAX requests
        if (typeof jQuery !== "undefined") {
            jQuery.ajaxSetup({
                cache: false,
                beforeSend: function(xhr) {
                    xhr.setRequestHeader("Cache-Control", "no-cache, no-store, must-revalidate");
                    xhr.setRequestHeader("Pragma", "no-cache");
                    xhr.setRequestHeader("Expires", "0");
                }
            });
        }
        </script>' . "\n";
    }
}
add_action('wp_head', 'ezpools_force_browser_refresh', 999);

// Force refresh of theme files by clearing any WordPress cache
function ezpools_clear_all_cache() {
    // Clear WordPress object cache
    if (function_exists('wp_cache_flush')) {
        wp_cache_flush();
    }

    // Clear any transients
    global $wpdb;
    $wpdb->query("DELETE FROM {$wpdb->options} WHERE option_name LIKE '_transient_%'");
    $wpdb->query("DELETE FROM {$wpdb->options} WHERE option_name LIKE '_site_transient_%'");

    // Clear rewrite rules cache
    flush_rewrite_rules();

    // Clear any plugin caches
    if (function_exists('w3tc_flush_all')) {
        w3tc_flush_all();
    }
    if (function_exists('wp_cache_clear_cache')) {
        wp_cache_clear_cache();
    }
    if (function_exists('rocket_clean_domain')) {
        rocket_clean_domain();
    }
    if (function_exists('sg_cachepress_purge_cache')) {
        sg_cachepress_purge_cache();
    }

    // Clear opcache if available
    if (function_exists('opcache_reset')) {
        opcache_reset();
    }
}
add_action('wp_loaded', 'ezpools_clear_all_cache');

// Force immediate cache clearing on every page load (development only)
function ezpools_force_immediate_refresh() {
    // Clear all WordPress caches immediately
    wp_cache_flush();

    // Clear any object cache
    if (function_exists('wp_cache_delete_group')) {
        wp_cache_delete_group('themes');
        wp_cache_delete_group('plugins');
    }

    // Force browser to reload by sending random ETag
    header('ETag: "' . md5(microtime() . rand()) . '"');

    // Send additional no-cache headers
    header('Vary: *');
    header('Last-Modified: ' . gmdate('D, d M Y H:i:s') . ' GMT');
}
add_action('template_redirect', 'ezpools_force_immediate_refresh', 1);

// Register widget areas
function ezpools_widgets_init() {
    register_sidebar(array(
        'name'          => __('Sidebar', 'ezpools'),
        'id'            => 'sidebar-1',
        'description'   => __('Add widgets here.', 'ezpools'),
        'before_widget' => '<section id="%1$s" class="widget %2$s">',
        'after_widget'  => '</section>',
        'before_title'  => '<h3 class="widget-title">',
        'after_title'   => '</h3>',
    ));
    
    register_sidebar(array(
        'name'          => __('Footer Widget Area 1', 'ezpools'),
        'id'            => 'footer-1',
        'description'   => __('Add widgets here for footer column 1.', 'ezpools'),
        'before_widget' => '<div id="%1$s" class="widget %2$s">',
        'after_widget'  => '</div>',
        'before_title'  => '<h3 class="widget-title">',
        'after_title'   => '</h3>',
    ));
    
    register_sidebar(array(
        'name'          => __('Footer Widget Area 2', 'ezpools'),
        'id'            => 'footer-2',
        'description'   => __('Add widgets here for footer column 2.', 'ezpools'),
        'before_widget' => '<div id="%1$s" class="widget %2$s">',
        'after_widget'  => '</div>',
        'before_title'  => '<h3 class="widget-title">',
        'after_title'   => '</h3>',
    ));
    
    register_sidebar(array(
        'name'          => __('Footer Widget Area 3', 'ezpools'),
        'id'            => 'footer-3',
        'description'   => __('Add widgets here for footer column 3.', 'ezpools'),
        'before_widget' => '<div id="%1$s" class="widget %2$s">',
        'after_widget'  => '</div>',
        'before_title'  => '<h3 class="widget-title">',
        'after_title'   => '</h3>',
    ));
}
add_action('widgets_init', 'ezpools_widgets_init');

// Custom excerpt length
function ezpools_excerpt_length($length) {
    return 20;
}
add_filter('excerpt_length', 'ezpools_excerpt_length');

// Custom excerpt more
function ezpools_excerpt_more($more) {
    return '...';
}
add_filter('excerpt_more', 'ezpools_excerpt_more');

// Add custom post types for pools
function ezpools_custom_post_types() {
    // Pool Products Post Type
    register_post_type('pool_product', array(
        'labels' => array(
            'name' => 'Pool Products',
            'singular_name' => 'Pool Product',
            'add_new' => 'Add New Pool Product',
            'add_new_item' => 'Add New Pool Product',
            'edit_item' => 'Edit Pool Product',
            'new_item' => 'New Pool Product',
            'view_item' => 'View Pool Product',
            'search_items' => 'Search Pool Products',
            'not_found' => 'No pool products found',
            'not_found_in_trash' => 'No pool products found in trash'
        ),
        'public' => true,
        'has_archive' => true,
        'supports' => array('title', 'editor', 'thumbnail', 'excerpt'),
        'menu_icon' => 'dashicons-admin-home',
        'rewrite' => array('slug' => 'pools'),
    ));
    
    // Pool Accessories Post Type
    register_post_type('pool_accessory', array(
        'labels' => array(
            'name' => 'Pool Accessories',
            'singular_name' => 'Pool Accessory',
            'add_new' => 'Add New Accessory',
            'add_new_item' => 'Add New Pool Accessory',
            'edit_item' => 'Edit Pool Accessory',
            'new_item' => 'New Pool Accessory',
            'view_item' => 'View Pool Accessory',
            'search_items' => 'Search Pool Accessories',
            'not_found' => 'No pool accessories found',
            'not_found_in_trash' => 'No pool accessories found in trash'
        ),
        'public' => true,
        'has_archive' => false,
        'supports' => array('title', 'editor', 'thumbnail', 'excerpt'),
        'menu_icon' => 'dashicons-admin-tools',
        'show_in_menu' => 'edit.php?post_type=pool_product',
        'rewrite' => array('slug' => 'accessories'),
    ));

    // Testimonials Post Type
    register_post_type('testimonial', array(
        'labels' => array(
            'name' => 'Testimonials',
            'singular_name' => 'Testimonial',
            'add_new' => 'Add New Testimonial',
            'add_new_item' => 'Add New Testimonial',
            'edit_item' => 'Edit Testimonial',
            'new_item' => 'New Testimonial',
            'view_item' => 'View Testimonial',
            'search_items' => 'Search Testimonials',
            'not_found' => 'No testimonials found',
            'not_found_in_trash' => 'No testimonials found in trash'
        ),
        'public' => true,
        'has_archive' => true,
        'supports' => array('title', 'editor', 'thumbnail'),
        'menu_icon' => 'dashicons-format-quote',
        'rewrite' => array('slug' => 'testimonials'),
    ));
}
add_action('init', 'ezpools_custom_post_types');

// Add custom taxonomies
function ezpools_custom_taxonomies() {
    // Pool Categories
    register_taxonomy('pool_category', 'pool_product', array(
        'labels' => array(
            'name' => 'Pool Categories',
            'singular_name' => 'Pool Category',
            'search_items' => 'Search Pool Categories',
            'all_items' => 'All Pool Categories',
            'edit_item' => 'Edit Pool Category',
            'update_item' => 'Update Pool Category',
            'add_new_item' => 'Add New Pool Category',
            'new_item_name' => 'New Pool Category Name',
            'menu_name' => 'Pool Categories',
        ),
        'hierarchical' => true,
        'public' => true,
        'rewrite' => array('slug' => 'pool-category'),
    ));
}
add_action('init', 'ezpools_custom_taxonomies');

// Customizer settings
function ezpools_customize_register($wp_customize) {
    // Hero Section
    $wp_customize->add_section('ezpools_hero', array(
        'title' => __('Hero Section', 'ezpools'),
        'priority' => 30,
    ));
    
    $wp_customize->add_setting('hero_title', array(
        'default' => 'The Better Portable Pool',
        'sanitize_callback' => 'sanitize_text_field',
    ));
    
    $wp_customize->add_control('hero_title', array(
        'label' => __('Hero Title', 'ezpools'),
        'section' => 'ezpools_hero',
        'type' => 'text',
    ));
    
    $wp_customize->add_setting('hero_subtitle', array(
        'default' => 'Custom portable pools made-to-order. From carton to completion in less than one hour.',
        'sanitize_callback' => 'sanitize_textarea_field',
    ));
    
    $wp_customize->add_control('hero_subtitle', array(
        'label' => __('Hero Subtitle', 'ezpools'),
        'section' => 'ezpools_hero',
        'type' => 'textarea',
    ));
    
    // Contact Information
    $wp_customize->add_section('ezpools_contact', array(
        'title' => __('Contact Information', 'ezpools'),
        'priority' => 35,
    ));
    
    $wp_customize->add_setting('contact_phone', array(
        'default' => '(855) 4EZ-POOL',
        'sanitize_callback' => 'sanitize_text_field',
    ));
    
    $wp_customize->add_control('contact_phone', array(
        'label' => __('Phone Number', 'ezpools'),
        'section' => 'ezpools_contact',
        'type' => 'text',
    ));
    
    $wp_customize->add_setting('contact_email', array(
        'default' => '<EMAIL>',
        'sanitize_callback' => 'sanitize_email',
    ));
    
    $wp_customize->add_control('contact_email', array(
        'label' => __('Email Address', 'ezpools'),
        'section' => 'ezpools_contact',
        'type' => 'email',
    ));
}
add_action('customize_register', 'ezpools_customize_register');

// Add custom meta boxes for pool products
function ezpools_add_meta_boxes() {
    add_meta_box(
        'pool_details',
        'Pool Details',
        'ezpools_pool_details_callback',
        'pool_product',
        'normal',
        'high'
    );
}
add_action('add_meta_boxes', 'ezpools_add_meta_boxes');

function ezpools_pool_details_callback($post) {
    wp_nonce_field('ezpools_save_pool_details', 'ezpools_pool_details_nonce');

    $width = get_post_meta($post->ID, '_pool_width', true);
    $length_min = get_post_meta($post->ID, '_pool_length_min', true);
    $length_max = get_post_meta($post->ID, '_pool_length_max', true);
    $depth = get_post_meta($post->ID, '_pool_depth', true);
    $price_range = get_post_meta($post->ID, '_pool_price_range', true);
    $base_price = get_post_meta($post->ID, '_pool_base_price', true);
    $shipping_cost = get_post_meta($post->ID, '_pool_shipping_cost', true);
    $gallons = get_post_meta($post->ID, '_pool_gallons', true);
    $footprint = get_post_meta($post->ID, '_pool_footprint', true);
    $top_dimensions = get_post_meta($post->ID, '_pool_top_dimensions', true);

    echo '<table class="form-table">';
    echo '<tr><th><label for="pool_width">Width (feet)</label></th>';
    echo '<td><input type="number" id="pool_width" name="pool_width" value="' . esc_attr($width) . '" /></td></tr>';
    echo '<tr><th><label for="pool_length_min">Minimum Length (feet)</label></th>';
    echo '<td><input type="number" id="pool_length_min" name="pool_length_min" value="' . esc_attr($length_min) . '" /></td></tr>';
    echo '<tr><th><label for="pool_length_max">Maximum Length (feet)</label></th>';
    echo '<td><input type="number" id="pool_length_max" name="pool_length_max" value="' . esc_attr($length_max) . '" /></td></tr>';
    echo '<tr><th><label for="pool_depth">Depth (feet)</label></th>';
    echo '<td><input type="text" id="pool_depth" name="pool_depth" value="' . esc_attr($depth) . '" /></td></tr>';
    echo '<tr><th><label for="pool_base_price">Base Price ($)</label></th>';
    echo '<td><input type="number" id="pool_base_price" name="pool_base_price" value="' . esc_attr($base_price) . '" /></td></tr>';
    echo '<tr><th><label for="pool_shipping_cost">Shipping Cost ($)</label></th>';
    echo '<td><input type="number" id="pool_shipping_cost" name="pool_shipping_cost" value="' . esc_attr($shipping_cost) . '" /></td></tr>';
    echo '<tr><th><label for="pool_gallons">Gallons</label></th>';
    echo '<td><input type="number" id="pool_gallons" name="pool_gallons" value="' . esc_attr($gallons) . '" /></td></tr>';
    echo '<tr><th><label for="pool_footprint">Footprint</label></th>';
    echo '<td><input type="text" id="pool_footprint" name="pool_footprint" value="' . esc_attr($footprint) . '" placeholder="e.g., 9.5\' x 13.5\'" /></td></tr>';
    echo '<tr><th><label for="pool_top_dimensions">Top Dimensions</label></th>';
    echo '<td><input type="text" id="pool_top_dimensions" name="pool_top_dimensions" value="' . esc_attr($top_dimensions) . '" placeholder="e.g., 6.5\' x 11.5\' x 52\"" /></td></tr>';
    echo '<tr><th><label for="pool_price_range">Price Range (for display)</label></th>';
    echo '<td><input type="text" id="pool_price_range" name="pool_price_range" value="' . esc_attr($price_range) . '" placeholder="e.g., $1,650 - $2,100" /></td></tr>';
    echo '</table>';
}

function ezpools_save_pool_details($post_id) {
    if (!isset($_POST['ezpools_pool_details_nonce']) || !wp_verify_nonce($_POST['ezpools_pool_details_nonce'], 'ezpools_save_pool_details')) {
        return;
    }
    
    if (defined('DOING_AUTOSAVE') && DOING_AUTOSAVE) {
        return;
    }
    
    if (!current_user_can('edit_post', $post_id)) {
        return;
    }
    
    if (isset($_POST['pool_width'])) {
        update_post_meta($post_id, '_pool_width', sanitize_text_field($_POST['pool_width']));
    }
    
    if (isset($_POST['pool_length_min'])) {
        update_post_meta($post_id, '_pool_length_min', sanitize_text_field($_POST['pool_length_min']));
    }
    
    if (isset($_POST['pool_length_max'])) {
        update_post_meta($post_id, '_pool_length_max', sanitize_text_field($_POST['pool_length_max']));
    }
    
    if (isset($_POST['pool_depth'])) {
        update_post_meta($post_id, '_pool_depth', sanitize_text_field($_POST['pool_depth']));
    }
    
    if (isset($_POST['pool_price_range'])) {
        update_post_meta($post_id, '_pool_price_range', sanitize_text_field($_POST['pool_price_range']));
    }

    if (isset($_POST['pool_base_price'])) {
        update_post_meta($post_id, '_pool_base_price', sanitize_text_field($_POST['pool_base_price']));
    }

    if (isset($_POST['pool_shipping_cost'])) {
        update_post_meta($post_id, '_pool_shipping_cost', sanitize_text_field($_POST['pool_shipping_cost']));
    }

    if (isset($_POST['pool_gallons'])) {
        update_post_meta($post_id, '_pool_gallons', sanitize_text_field($_POST['pool_gallons']));
    }

    if (isset($_POST['pool_footprint'])) {
        update_post_meta($post_id, '_pool_footprint', sanitize_text_field($_POST['pool_footprint']));
    }

    if (isset($_POST['pool_top_dimensions'])) {
        update_post_meta($post_id, '_pool_top_dimensions', sanitize_text_field($_POST['pool_top_dimensions']));
    }
}
add_action('save_post', 'ezpools_save_pool_details');
?>
