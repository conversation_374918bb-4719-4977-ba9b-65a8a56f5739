<?php
/**
 * Pool Data Migration Script
 * Run this script to import pool products from the old website data
 * 
 * Usage: Access via WordPress admin or run via WP-CLI
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

class EZPoolDataMigration {
    
    private $pool_data = array();
    
    public function __construct() {
        $this->init_pool_data();
    }
    
    /**
     * Initialize pool product data based on old website analysis
     */
    private function init_pool_data() {
        $this->pool_data = array(
            // 7-Wide Lap Pools
            array(
                'title' => '7\' x 12\' EZ Pool',
                'width' => 7,
                'length_min' => 12,
                'length_max' => 12,
                'depth' => '52"',
                'base_price' => 1650,
                'shipping_cost' => 450,
                'gallons' => 2500,
                'footprint' => '9.5\' x 13.5\'',
                'top_dimensions' => '6.5\' x 11.5\' x 52"',
                'category' => '7-wide-lap',
                'description' => 'Perfect for lap swimming and exercise. Compact design fits in most backyards.',
                'features' => array(
                    'Made-to-Order in the USA',
                    '5-Year Warranty',
                    'American-Made Components',
                    'Includes Flo-Kit Set'
                )
            ),
            array(
                'title' => '7\' x 17\' EZ Pool',
                'width' => 7,
                'length_min' => 17,
                'length_max' => 17,
                'depth' => '52"',
                'base_price' => 2100,
                'shipping_cost' => 450,
                'gallons' => 3500,
                'footprint' => '9.5\' x 18.5\'',
                'top_dimensions' => '6.5\' x 16.5\' x 52"',
                'category' => '7-wide-lap',
                'description' => 'Extended length for serious lap swimming and aquatic exercise.',
            ),
            array(
                'title' => '7\' x 22\' EZ Pool',
                'width' => 7,
                'length_min' => 22,
                'length_max' => 22,
                'depth' => '52"',
                'base_price' => 2550,
                'shipping_cost' => 450,
                'gallons' => 4500,
                'footprint' => '9.5\' x 23.5\'',
                'top_dimensions' => '6.5\' x 21.5\' x 52"',
                'category' => '7-wide-lap',
                'description' => 'Professional length lap pool for serious swimmers.',
            ),
            
            // 12-Wide Family Pools
            array(
                'title' => '12\' x 12\' EZ Pool',
                'width' => 12,
                'length_min' => 12,
                'length_max' => 12,
                'depth' => '52"',
                'base_price' => 2400,
                'shipping_cost' => 450,
                'gallons' => 5000,
                'footprint' => '14.5\' x 13.5\'',
                'top_dimensions' => '11.5\' x 11.5\' x 52"',
                'category' => '12-wide-family',
                'description' => 'Perfect square pool for family fun and recreation.',
            ),
            array(
                'title' => '12\' x 17\' EZ Pool',
                'width' => 12,
                'length_min' => 17,
                'length_max' => 17,
                'depth' => '52"',
                'base_price' => 3000,
                'shipping_cost' => 450,
                'gallons' => 7000,
                'footprint' => '14.5\' x 18.5\'',
                'top_dimensions' => '11.5\' x 16.5\' x 52"',
                'category' => '12-wide-family',
                'description' => 'Spacious family pool with room for swimming and play.',
            ),
            array(
                'title' => '12\' x 22\' EZ Pool',
                'width' => 12,
                'length_min' => 22,
                'length_max' => 22,
                'depth' => '52"',
                'base_price' => 3600,
                'shipping_cost' => 450,
                'gallons' => 9000,
                'footprint' => '14.5\' x 23.5\'',
                'top_dimensions' => '11.5\' x 21.5\' x 52"',
                'category' => '12-wide-family',
                'description' => 'Large family pool perfect for entertaining and exercise.',
            ),
            
            // 17-Wide Super Pools
            array(
                'title' => '17\' x 17\' EZ Pool',
                'width' => 17,
                'length_min' => 17,
                'length_max' => 17,
                'depth' => '52"',
                'base_price' => 4200,
                'shipping_cost' => 450,
                'gallons' => 12000,
                'footprint' => '19.5\' x 18.5\'',
                'top_dimensions' => '16.5\' x 16.5\' x 52"',
                'category' => '17-wide-super',
                'description' => 'Super-sized square pool for maximum family enjoyment.',
            ),
            array(
                'title' => '17\' x 22\' EZ Pool',
                'width' => 17,
                'length_min' => 22,
                'length_max' => 22,
                'depth' => '52"',
                'base_price' => 5000,
                'shipping_cost' => 450,
                'gallons' => 15000,
                'footprint' => '19.5\' x 23.5\'',
                'top_dimensions' => '16.5\' x 21.5\' x 52"',
                'category' => '17-wide-super',
                'description' => 'Premium large pool for serious swimmers and families.',
            ),
            
            // 22-Wide Giant Pools
            array(
                'title' => '22\' x 22\' EZ Pool',
                'width' => 22,
                'length_min' => 22,
                'length_max' => 22,
                'depth' => '52"',
                'base_price' => 6500,
                'shipping_cost' => 450,
                'gallons' => 20000,
                'footprint' => '24.5\' x 23.5\'',
                'top_dimensions' => '21.5\' x 21.5\' x 52"',
                'category' => '22-wide-giant',
                'description' => 'Giant square pool for ultimate backyard luxury.',
            ),
            array(
                'title' => '22\' x 27\' EZ Pool',
                'width' => 22,
                'length_min' => 27,
                'length_max' => 27,
                'depth' => '52"',
                'base_price' => 7200,
                'shipping_cost' => 450,
                'gallons' => 24000,
                'footprint' => '24.5\' x 28.5\'',
                'top_dimensions' => '21.5\' x 26.5\' x 52"',
                'category' => '22-wide-giant',
                'description' => 'Massive pool for commercial or luxury residential use.',
            ),
        );
        
        // Initialize accessory data
        $this->accessory_data = array(
            array(
                'title' => 'A-Frame Ladder',
                'price' => 400,
                'shipping' => 35,
                'description' => 'Essential for safe pool entry and exit. Heavy-duty construction.',
                'features' => array('Heavy-duty construction', 'Non-slip steps', 'Corrosion resistant')
            ),
            array(
                'title' => 'Inside Step',
                'price' => 500,
                'shipping' => 35,
                'description' => 'Convenient in-pool step for easy access and safety.',
                'features' => array('In-pool installation', 'Non-slip surface', 'Durable construction')
            ),
            array(
                'title' => 'EZ Pool Care Kit',
                'price' => 125,
                'shipping' => 0,
                'description' => 'Everything needed for pool maintenance except sanitizer.',
                'features' => array('Complete maintenance kit', 'Professional grade tools', 'Easy to use')
            ),
            array(
                'title' => 'Extra Flo-Kit',
                'price' => 65,
                'shipping' => 10,
                'description' => 'Additional flow kit for enhanced water circulation.',
                'features' => array('Enhanced circulation', 'Easy installation', 'Durable materials')
            ),
            array(
                'title' => 'Solar Cover',
                'price' => 100,
                'shipping' => 0,
                'description' => 'Helps keep pool clean and retains heat during sunny days.',
                'features' => array('Heat retention', 'Debris protection', 'UV resistant')
            ),
            array(
                'title' => 'Pool Cover',
                'price' => 300,
                'shipping' => 0,
                'description' => 'Heavy-duty cover for winterizing and protection.',
                'features' => array('Winterizing protection', 'Heavy-duty material', 'Custom fit')
            ),
            array(
                'title' => 'EZ SwimMill',
                'price' => 50,
                'shipping' => 10,
                'description' => 'Swimming treadmill for enhanced workouts and resistance training.',
                'features' => array('Resistance training', 'Adjustable flow', 'Easy installation')
            ),
            array(
                'title' => '4-Way Outdoor Timer',
                'price' => 45,
                'shipping' => 0,
                'description' => 'Automated timer for pool equipment control.',
                'features' => array('4-way control', 'Weather resistant', 'Easy programming')
            ),
        );
    }
    
    /**
     * Run the migration
     */
    public function run_migration() {
        $results = array(
            'pools_created' => 0,
            'accessories_created' => 0,
            'categories_created' => 0,
            'errors' => array()
        );
        
        try {
            // Create pool categories first
            $this->create_pool_categories();
            $results['categories_created'] = 4;
            
            // Create pool products
            foreach ($this->pool_data as $pool) {
                if ($this->create_pool_product($pool)) {
                    $results['pools_created']++;
                } else {
                    $results['errors'][] = 'Failed to create pool: ' . $pool['title'];
                }
            }
            
            // Create accessories
            foreach ($this->accessory_data as $accessory) {
                if ($this->create_pool_accessory($accessory)) {
                    $results['accessories_created']++;
                } else {
                    $results['errors'][] = 'Failed to create accessory: ' . $accessory['title'];
                }
            }
            
        } catch (Exception $e) {
            $results['errors'][] = 'Migration error: ' . $e->getMessage();
        }
        
        return $results;
    }
    
    /**
     * Create pool categories
     */
    private function create_pool_categories() {
        $categories = array(
            array('name' => '7\' Wide Lap Pools', 'slug' => '7-wide-lap'),
            array('name' => '12\' Wide Family Pools', 'slug' => '12-wide-family'),
            array('name' => '17\' Wide Super Pools', 'slug' => '17-wide-super'),
            array('name' => '22\' Wide Giant Pools', 'slug' => '22-wide-giant'),
        );
        
        foreach ($categories as $category) {
            if (!term_exists($category['slug'], 'pool_category')) {
                wp_insert_term($category['name'], 'pool_category', array(
                    'slug' => $category['slug']
                ));
            }
        }
    }
    
    /**
     * Create a pool product
     */
    private function create_pool_product($pool_data) {
        // Check if product already exists
        $existing = get_page_by_title($pool_data['title'], OBJECT, 'pool_product');
        if ($existing) {
            return false; // Already exists
        }
        
        // Create the post
        $post_data = array(
            'post_title' => $pool_data['title'],
            'post_content' => $this->generate_pool_description($pool_data),
            'post_status' => 'publish',
            'post_type' => 'pool_product',
            'post_excerpt' => $pool_data['description']
        );
        
        $post_id = wp_insert_post($post_data);
        
        if ($post_id && !is_wp_error($post_id)) {
            // Add meta fields
            update_post_meta($post_id, '_pool_width', $pool_data['width']);
            update_post_meta($post_id, '_pool_length_min', $pool_data['length_min']);
            update_post_meta($post_id, '_pool_length_max', $pool_data['length_max']);
            update_post_meta($post_id, '_pool_depth', $pool_data['depth']);
            update_post_meta($post_id, '_pool_base_price', $pool_data['base_price']);
            update_post_meta($post_id, '_pool_shipping_cost', $pool_data['shipping_cost']);
            update_post_meta($post_id, '_pool_gallons', $pool_data['gallons']);
            update_post_meta($post_id, '_pool_footprint', $pool_data['footprint']);
            update_post_meta($post_id, '_pool_top_dimensions', $pool_data['top_dimensions']);
            update_post_meta($post_id, '_pool_price_range', 'Starting at $' . number_format($pool_data['base_price']));
            
            // Assign category
            wp_set_object_terms($post_id, $pool_data['category'], 'pool_category');
            
            return true;
        }
        
        return false;
    }
    
    /**
     * Create a pool accessory
     */
    private function create_pool_accessory($accessory_data) {
        // Check if accessory already exists
        $existing = get_page_by_title($accessory_data['title'], OBJECT, 'pool_accessory');
        if ($existing) {
            return false; // Already exists
        }
        
        // Create the post
        $post_data = array(
            'post_title' => $accessory_data['title'],
            'post_content' => $this->generate_accessory_description($accessory_data),
            'post_status' => 'publish',
            'post_type' => 'pool_accessory',
            'post_excerpt' => $accessory_data['description']
        );
        
        $post_id = wp_insert_post($post_data);
        
        if ($post_id && !is_wp_error($post_id)) {
            // Add meta fields
            update_post_meta($post_id, '_accessory_price', $accessory_data['price']);
            update_post_meta($post_id, '_accessory_shipping', $accessory_data['shipping']);
            
            return true;
        }
        
        return false;
    }
    
    /**
     * Generate pool description
     */
    private function generate_pool_description($pool_data) {
        $description = "<p>{$pool_data['description']}</p>";
        
        $description .= "<h3>Specifications</h3>";
        $description .= "<ul>";
        $description .= "<li><strong>Size:</strong> {$pool_data['width']}' x {$pool_data['length_min']}'</li>";
        $description .= "<li><strong>Depth:</strong> {$pool_data['depth']}</li>";
        $description .= "<li><strong>Capacity:</strong> " . number_format($pool_data['gallons']) . " gallons</li>";
        $description .= "<li><strong>Footprint:</strong> {$pool_data['footprint']}</li>";
        $description .= "<li><strong>Top Dimensions:</strong> {$pool_data['top_dimensions']}</li>";
        $description .= "</ul>";
        
        $description .= "<h3>Features</h3>";
        $description .= "<ul>";
        $description .= "<li>Made-to-Order in the USA</li>";
        $description .= "<li>5-Year Warranty</li>";
        $description .= "<li>American-Made Components</li>";
        $description .= "<li>Includes Flo-Kit Set</li>";
        $description .= "<li>Ships in 10-14 Business Days</li>";
        $description .= "</ul>";
        
        return $description;
    }
    
    /**
     * Generate accessory description
     */
    private function generate_accessory_description($accessory_data) {
        $description = "<p>{$accessory_data['description']}</p>";
        
        if (isset($accessory_data['features']) && is_array($accessory_data['features'])) {
            $description .= "<h3>Features</h3>";
            $description .= "<ul>";
            foreach ($accessory_data['features'] as $feature) {
                $description .= "<li>{$feature}</li>";
            }
            $description .= "</ul>";
        }
        
        return $description;
    }
}

// Admin interface for running migration
if (is_admin()) {
    add_action('admin_menu', function() {
        add_submenu_page(
            'edit.php?post_type=pool_product',
            'Import Pool Data',
            'Import Data',
            'manage_options',
            'pool-data-migration',
            'ezpools_migration_page'
        );
    });
}

function ezpools_migration_page() {
    if (isset($_POST['run_migration']) && wp_verify_nonce($_POST['migration_nonce'], 'run_pool_migration')) {
        $migration = new EZPoolDataMigration();
        $results = $migration->run_migration();
        
        echo '<div class="notice notice-success"><p>';
        echo "Migration completed! ";
        echo "Created {$results['pools_created']} pools, ";
        echo "{$results['accessories_created']} accessories, ";
        echo "and {$results['categories_created']} categories.";
        if (!empty($results['errors'])) {
            echo "<br>Errors: " . implode(', ', $results['errors']);
        }
        echo '</p></div>';
    }
    
    ?>
    <div class="wrap">
        <h1>Pool Data Migration</h1>
        <p>This will import pool products and accessories from the old website data.</p>
        
        <form method="post">
            <?php wp_nonce_field('run_pool_migration', 'migration_nonce'); ?>
            <p>
                <input type="submit" name="run_migration" class="button button-primary" value="Run Migration" 
                       onclick="return confirm('This will create new pool products. Continue?');">
            </p>
        </form>
        
        <h2>What will be imported:</h2>
        <ul>
            <li>4 Pool Categories (7' Wide, 12' Wide, 17' Wide, 22' Wide)</li>
            <li>10+ Pool Products with specifications and pricing</li>
            <li>8 Pool Accessories with pricing and descriptions</li>
        </ul>
    </div>
    <?php
}
?>
