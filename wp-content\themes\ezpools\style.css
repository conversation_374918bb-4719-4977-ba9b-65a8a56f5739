/*
Theme Name: EZ Pools Modern
Description: A modern, responsive WordPress theme for EZ Pools - The Better Portable Pool
Version: 2.0
Author: EZ Pools
*/

/* Import Modern Fonts */
@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&family=Poppins:wght@300;400;500;600;700;800&display=swap');

/* CSS Custom Properties */
:root {
    --primary-color: #0066cc;
    --primary-dark: #004499;
    --secondary-color: #00a8ff;
    --accent-color: #ffcc00;
    --text-dark: #1a1a1a;
    --text-light: #666;
    --white: #ffffff;
    --light-bg: #f8fafc;
    --glass-bg: rgba(255, 255, 255, 0.1);
    --glass-border: rgba(255, 255, 255, 0.2);
    --shadow-light: 0 4px 6px rgba(0, 0, 0, 0.05);
    --shadow-medium: 0 10px 25px rgba(0, 0, 0, 0.1);
    --shadow-heavy: 0 20px 40px rgba(0, 0, 0, 0.15);
    --border-radius: 12px;
    --transition-fast: 0.2s ease;
    --transition-medium: 0.4s ease;
    --transition-slow: 0.6s ease;
}

/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

html {
    scroll-behavior: smooth;
}

body {
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    line-height: 1.6;
    color: var(--text-dark);
    background-color: var(--light-bg);
    overflow-x: hidden;
    padding-top: 80px;
}

body.menu-open {
    overflow: hidden;
}

body.loading {
    overflow: hidden;
}

body.loading .site-header {
    opacity: 0;
    transform: translateY(-20px);
}

body.loading .hero,
body.loading .features,
body.loading .products {
    opacity: 0;
    transform: translateY(20px);
}

body.loaded .site-header {
    opacity: 1;
    transform: translateY(0);
    transition: all 0.6s ease;
}

body.loaded .hero,
body.loaded .features,
body.loaded .products {
    opacity: 1;
    transform: translateY(0);
    transition: all 1s ease;
}

/* Container */
.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

/* Modern Header with Enhanced Pool Water Colors and Effects */
.site-header {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    z-index: 1000;
    /* Enhanced gradient background that matches pool water colors */
    background: linear-gradient(
        180deg,
        rgba(0, 150, 255, 0.92) 0%,     /* Bright pool blue at top */
        rgba(0, 120, 200, 0.94) 30%,    /* Medium pool blue */
        rgba(0, 90, 160, 0.96) 70%,     /* Deeper blue */
        rgba(0, 40, 80, 0.98) 100%      /* Dark blue-black at bottom */
    );
    backdrop-filter: blur(20px);
    -webkit-backdrop-filter: blur(20px);
    padding: 1rem 0;
    /* Enhanced drop shadow at bottom */
    box-shadow:
        0 4px 12px rgba(0, 0, 0, 0.25),
        0 8px 24px rgba(0, 0, 0, 0.15),
        0 12px 36px rgba(0, 0, 0, 0.1);
}

.site-header.scrolled {
    /* Slightly more opaque when scrolled */
    background: linear-gradient(
        180deg,
        rgba(0, 150, 255, 0.95) 0%,
        rgba(0, 120, 200, 0.96) 30%,
        rgba(0, 90, 160, 0.98) 70%,
        rgba(0, 40, 80, 0.99) 100%
    );
    backdrop-filter: blur(25px);
    -webkit-backdrop-filter: blur(25px);
    box-shadow:
        0 6px 16px rgba(0, 0, 0, 0.3),
        0 12px 32px rgba(0, 0, 0, 0.2),
        0 16px 48px rgba(0, 0, 0, 0.15);
    padding: 1rem 0;
}

.site-header.header-hidden {
    transform: translateY(-100%);
}

.header-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-wrap: wrap;
    position: relative;
}

.logo {
    font-family: 'Poppins', sans-serif;
    font-size: 1.75rem;
    font-weight: 700;
    color: var(--white);
    text-decoration: none;
    transition: all var(--transition-fast);
    position: relative;
}

.logo::after {
    content: '';
    position: absolute;
    bottom: -2px;
    left: 0;
    width: 0;
    height: 2px;
    background: linear-gradient(90deg, var(--accent-color), var(--secondary-color));
    transition: width var(--transition-medium);
}

.logo:hover::after {
    width: 100%;
}

.logo:hover {
    color: var(--accent-color);
    transform: translateY(-1px);
}

/* Modern Navigation */
.main-nav .nav-menu {
    display: flex;
    list-style: none;
    gap: 2rem;
    margin: 0;
    padding: 0;
}

.main-nav .nav-menu li {
    position: relative;
}

.main-nav .nav-menu a {
    color: var(--white);
    text-decoration: none;
    font-weight: 500;
    font-size: 0.95rem;
    padding: 0.75rem 1.25rem;
    border-radius: 8px;
    transition: all 0.3s ease;
    position: relative;
    display: block;
}

.main-nav .nav-menu a:hover,
.main-nav .nav-menu a:focus {
    background: rgba(255, 255, 255, 0.15);
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    outline: none;
}

.main-nav .nav-menu a.current-menu-item,
.main-nav .nav-menu a.current_page_item {
    background: rgba(255, 255, 255, 0.2);
    font-weight: 600;
}

.main-nav .nav-menu a:focus {
    outline: 2px solid var(--accent-color);
    outline-offset: 2px;
}

/* Mobile Menu Toggle */
.mobile-menu-toggle {
    display: none;
    flex-direction: column;
    gap: 5px;
    background: none;
    border: none;
    cursor: pointer;
    padding: 0.75rem;
    border-radius: 8px;
    transition: all 0.3s ease;
    position: relative;
    z-index: 1001;
}

.mobile-menu-toggle:hover {
    background: rgba(255, 255, 255, 0.1);
}

.mobile-menu-toggle span {
    width: 24px;
    height: 2px;
    background: var(--white);
    border-radius: 2px;
    transition: all 0.3s ease;
    transform-origin: center;
}

.mobile-menu-toggle.active span:nth-child(1) {
    transform: rotate(45deg) translate(0, 7px);
}

.mobile-menu-toggle.active span:nth-child(2) {
    opacity: 0;
    transform: scale(0);
}

.mobile-menu-toggle.active span:nth-child(3) {
    transform: rotate(-45deg) translate(0, -7px);
}

/* Hero Section with Pool Waves Background */
.hero {
    position: relative;
    min-height: 100vh;
    display: flex;
    align-items: center;
    background-image: url('images/pool-water-ripples.jpg');
    background-size: cover;
    background-position: center top;
    background-repeat: no-repeat;
    background-attachment: fixed;
    color: var(--white);
    text-align: center;
    overflow: hidden;
}

/* Hero Background Overlay for better text readability */
.hero-background {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 102, 204, 0.3);
    z-index: 1;
}

/* All moving CSS elements removed - keeping only JavaScript particles */

/* Animation keyframes for moving elements removed */

/* Enhanced responsive background coverage */
@media (max-width: 768px) {
    .hero {
        background-attachment: scroll; /* Fixed attachment can cause issues on mobile */
        background-size: cover;
        background-position: center top;
    }
}

/* For desktop, ensure fixed attachment works properly */
@media (min-width: 769px) {
    .hero {
        background-attachment: fixed;
    }
}

/* Ensure full coverage on all aspect ratios */
@media (max-aspect-ratio: 1/1) {
    .hero {
        background-size: cover;
        background-position: center top;
    }
}

@media (min-aspect-ratio: 2/1) {
    .hero {
        background-size: cover;
        background-position: center top;
    }
}

/* Force full coverage on very wide screens */
@media (min-width: 1920px) {
    .hero {
        background-size: 100% 100%;
        background-position: center top;
    }
}

/* Wave bottom effect - keeping for visual continuity */
.hero .wave-bottom {
    position: absolute;
    bottom: 0;
    left: 0;
    width: 100%;
    height: 80px;
    background: linear-gradient(to top, rgba(255, 255, 255, 0.08) 0%, transparent 100%);
    clip-path: polygon(0 60%, 100% 80%, 100% 100%, 0% 100%);
    z-index: 5;
    animation: gentleWaves 10s ease-in-out infinite;
}



@keyframes gentleWaves {
    0%, 100% {
        transform: translateY(0px);
        opacity: 0.6;
    }
    50% {
        transform: translateY(-10px);
        opacity: 0.8;
    }
}

.hero .container {
    position: relative;
    z-index: 30;
    backdrop-filter: blur(1px);
    -webkit-backdrop-filter: blur(1px);
}

/* Enhanced pool-themed visual elements */
.hero .container::before {
    content: '';
    position: absolute;
    top: -50px;
    left: -50px;
    right: -50px;
    bottom: -50px;
    background:
        /* Subtle ripple effects */
        radial-gradient(circle at 25% 25%, rgba(255, 255, 255, 0.1) 1px, transparent 1px),
        radial-gradient(circle at 75% 75%, rgba(255, 255, 255, 0.08) 1px, transparent 1px);
    background-size: 60px 60px, 80px 80px;
    animation: ripple 15s linear infinite;
    pointer-events: none;
    z-index: -1;
}

@keyframes ripple {
    0% {
        background-position: 0 0, 0 0;
    }
    100% {
        background-position: 60px 60px, -80px -80px;
    }
}



.hero h1 {
    font-family: 'Poppins', sans-serif;
    font-size: clamp(2.5rem, 5vw, 4rem);
    font-weight: 800;
    margin-bottom: 1.5rem;
    color: var(--white);
    text-shadow:
        0 2px 4px rgba(0, 0, 0, 0.9),
        0 4px 8px rgba(0, 0, 0, 0.8),
        0 8px 16px rgba(0, 0, 0, 0.6),
        0 0 40px rgba(0, 0, 0, 0.9);
    animation: fadeInUp 1s ease 0.2s both;
}

.hero p {
    font-size: clamp(1.1rem, 2vw, 1.4rem);
    margin-bottom: 3rem;
    max-width: 700px;
    margin-left: auto;
    margin-right: auto;
    color: var(--white);
    opacity: 0.95;
    line-height: 1.7;
    text-shadow:
        0 2px 4px rgba(0, 0, 0, 0.9),
        0 4px 8px rgba(0, 0, 0, 0.8),
        0 8px 16px rgba(0, 0, 0, 0.6),
        0 0 30px rgba(0, 0, 0, 0.9);
    animation: fadeInUp 1s ease 0.4s both;
}

/* Hero Buttons with Modern Design */
.hero-buttons {
    display: flex;
    gap: 1.5rem;
    justify-content: center;
    margin-bottom: 4rem;
    flex-wrap: wrap;
    animation: fadeInUp 1s ease 0.6s both;
}

.cta-button {
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    padding: 1.2rem 2.5rem;
    border-radius: 50px;
    text-decoration: none;
    font-weight: 600;
    font-size: 1.1rem;
    transition: all var(--transition-medium);
    border: 2px solid transparent;
    position: relative;
    overflow: hidden;
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
}

.cta-button::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left var(--transition-medium);
}

.cta-button:hover::before {
    left: 100%;
}

.cta-button.primary {
    background: linear-gradient(135deg, var(--accent-color) 0%, #ff9900 100%);
    color: var(--text-dark);
    box-shadow: var(--shadow-medium);
}

.cta-button.primary:hover {
    background: linear-gradient(135deg, #ff9900 0%, var(--accent-color) 100%);
    transform: translateY(-4px) scale(1.02);
    box-shadow: 0 15px 35px rgba(255, 204, 0, 0.4);
}

.cta-button.secondary {
    background: rgba(0, 0, 0, 0.4);
    color: var(--white);
    border: 2px solid rgba(255, 255, 255, 0.6);
    backdrop-filter: blur(15px);
    -webkit-backdrop-filter: blur(15px);
    box-shadow:
        0 8px 32px rgba(0, 0, 0, 0.3),
        inset 0 1px 0 rgba(255, 255, 255, 0.2);
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.8);
}

.cta-button.secondary:hover {
    background: var(--white);
    color: var(--primary-color);
    border-color: var(--white);
    transform: translateY(-4px) scale(1.02);
    box-shadow:
        0 15px 40px rgba(255, 255, 255, 0.3),
        0 8px 32px rgba(0, 0, 0, 0.2);
    text-shadow: none;
}

/* Hero Stats with Modern Cards */
.hero-stats {
    display: flex;
    justify-content: center;
    gap: 2rem;
    flex-wrap: wrap;
    animation: fadeInUp 1s ease 0.8s both;
}

.stat {
    background: rgba(0, 0, 0, 0.5);
    backdrop-filter: blur(20px);
    -webkit-backdrop-filter: blur(20px);
    border: 2px solid rgba(255, 255, 255, 0.4);
    border-radius: var(--border-radius);
    padding: 2rem 1.5rem;
    text-align: center;
    min-width: 150px;
    transition: all var(--transition-medium);
    box-shadow:
        0 8px 32px rgba(0, 0, 0, 0.4),
        inset 0 1px 0 rgba(255, 255, 255, 0.2);
}

.stat:hover {
    transform: translateY(-5px);
    background: rgba(0, 0, 0, 0.6);
    border-color: rgba(255, 255, 255, 0.6);
    box-shadow:
        0 12px 40px rgba(0, 0, 0, 0.5),
        inset 0 1px 0 rgba(255, 255, 255, 0.3);
}

.stat-number {
    display: block;
    font-family: 'Poppins', sans-serif;
    font-size: 2.5rem;
    font-weight: 800;
    color: var(--accent-color);
    margin-bottom: 0.5rem;
    text-shadow:
        0 2px 4px rgba(0, 0, 0, 0.8),
        0 4px 8px rgba(0, 0, 0, 0.6),
        0 0 20px rgba(255, 204, 0, 0.6),
        0 0 40px rgba(255, 204, 0, 0.3);
}

.stat-label {
    font-size: 0.9rem;
    font-weight: 600;
    color: var(--white);
    text-shadow:
        0 2px 4px rgba(0, 0, 0, 0.8),
        0 4px 8px rgba(0, 0, 0, 0.6);
}

/* Keyframe Animations */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes slideInLeft {
    from {
        opacity: 0;
        transform: translateX(-50px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

@keyframes slideInRight {
    from {
        opacity: 0;
        transform: translateX(50px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

@keyframes pulse {
    0%, 100% {
        transform: scale(1);
    }
    50% {
        transform: scale(1.05);
    }
}

@keyframes float {
    0%, 100% {
        transform: translateY(0px);
    }
    50% {
        transform: translateY(-10px);
    }
}

/* Enhanced particle animations */
@keyframes floatGentle {
    0%, 100% {
        transform: translateY(0px) translateX(0px) scale(1);
        opacity: 0.6;
    }
    25% {
        transform: translateY(-15px) translateX(10px) scale(1.1);
        opacity: 1;
    }
    50% {
        transform: translateY(-25px) translateX(-5px) scale(0.9);
        opacity: 0.8;
    }
    75% {
        transform: translateY(-10px) translateX(15px) scale(1.05);
        opacity: 0.9;
    }
}

@keyframes randomFlash {
    0%, 70% {
        opacity: 0;
        transform: scale(0.5);
    }
    75% {
        opacity: 0.3;
        transform: scale(0.8);
    }
    80% {
        opacity: 1;
        transform: scale(1.2);
    }
    85% {
        opacity: 0.7;
        transform: scale(1);
    }
    90%, 100% {
        opacity: 0;
        transform: scale(0.5);
    }
}

@keyframes subtleFloat {
    0%, 100% {
        transform: translateY(0px) translateX(0px);
    }
    50% {
        transform: translateY(-8px) translateX(5px);
    }
}

@keyframes shimmer {
    0%, 100% {
        box-shadow: 0 0 10px rgba(255, 255, 255, 0.2);
        filter: brightness(1);
    }
    50% {
        box-shadow: 0 0 20px rgba(255, 255, 255, 0.4);
        filter: brightness(1.3);
    }
}

@keyframes fadeInOut {
    0%, 100% {
        opacity: 0;
        transform: scaleY(0.5);
    }
    50% {
        opacity: 1;
        transform: scaleY(1);
    }
}

@keyframes gentleSway {
    0%, 100% {
        transform: rotate(-2deg) translateX(0px);
    }
    25% {
        transform: rotate(1deg) translateX(5px);
    }
    50% {
        transform: rotate(-1deg) translateX(-3px);
    }
    75% {
        transform: rotate(2deg) translateX(2px);
    }
}

/* Sun flare animations */
@keyframes sunFlareGlow {
    0%, 100% {
        opacity: 0.2;
        transform: scale(0.8);
    }
    25% {
        opacity: 0.6;
        transform: scale(1.1);
    }
    50% {
        opacity: 1;
        transform: scale(1.3);
    }
    75% {
        opacity: 0.7;
        transform: scale(1.0);
    }
}

@keyframes gentleMove {
    0%, 100% {
        transform: translateX(0px) translateY(0px);
    }
    25% {
        transform: translateX(10px) translateY(-5px);
    }
    50% {
        transform: translateX(-5px) translateY(-10px);
    }
    75% {
        transform: translateX(5px) translateY(-3px);
    }
}

@keyframes sunReflectionPulse {
    0%, 100% {
        opacity: 0.3;
        transform: scaleX(1) scaleY(0.8);
    }
    33% {
        opacity: 0.8;
        transform: scaleX(1.2) scaleY(1.0);
    }
    66% {
        opacity: 1;
        transform: scaleX(1.4) scaleY(1.2);
    }
}

@keyframes horizontalFloat {
    0%, 100% {
        transform: translateX(0px);
    }
    50% {
        transform: translateX(15px);
    }
}

/* Features Section with Modern Design */
.features {
    padding: 8rem 0;
    background: linear-gradient(135deg, var(--light-bg) 0%, var(--white) 100%);
    position: relative;
    overflow: hidden;
}

.features::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: radial-gradient(circle at 80% 20%, rgba(0, 102, 204, 0.05) 0%, transparent 50%);
    pointer-events: none;
}

.features .container {
    position: relative;
    z-index: 2;
}

.features h2 {
    text-align: center;
    font-family: 'Poppins', sans-serif;
    font-size: clamp(2rem, 4vw, 3rem);
    font-weight: 700;
    margin-bottom: 4rem;
    color: var(--text-dark);
    position: relative;
}

.features h2::after {
    content: '';
    position: absolute;
    bottom: -10px;
    left: 50%;
    transform: translateX(-50%);
    width: 80px;
    height: 4px;
    background: linear-gradient(90deg, var(--primary-color), var(--secondary-color));
    border-radius: 2px;
}

.features-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: 2.5rem;
    margin-top: 4rem;
}

.feature-card {
    background: var(--white);
    padding: 3rem 2rem;
    border-radius: 20px;
    text-align: center;
    box-shadow: var(--shadow-light);
    border: 1px solid rgba(0, 102, 204, 0.1);
    transition: all var(--transition-medium);
    position: relative;
    overflow: hidden;
}

.feature-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, transparent 0%, rgba(0, 102, 204, 0.02) 100%);
    opacity: 0;
    transition: opacity var(--transition-medium);
}

.feature-card:hover {
    transform: translateY(-8px) scale(1.02);
    box-shadow: var(--shadow-heavy);
    border-color: var(--primary-color);
}

.feature-card:hover::before {
    opacity: 1;
}

.feature-icon {
    font-size: 3.5rem;
    margin-bottom: 1.5rem;
    transition: all var(--transition-medium);
    animation: float 3s ease-in-out infinite;
}

.feature-card:hover .feature-icon {
    transform: scale(1.1);
    animation-play-state: paused;
}

.feature-card h3 {
    font-family: 'Poppins', sans-serif;
    font-size: 1.5rem;
    font-weight: 600;
    margin-bottom: 1rem;
    color: var(--text-dark);
    transition: color var(--transition-fast);
}

.feature-card:hover h3 {
    color: var(--primary-color);
}

.feature-card p {
    color: var(--text-light);
    line-height: 1.7;
    font-size: 1rem;
}

/* Products Section */
.products {
    padding: 5rem 0;
    background-image: url('images/pool-water-ripples.jpg');
    background-size: cover;
    background-position: center;
    background-repeat: no-repeat;
    background-attachment: fixed;
    position: relative;
}

/* Add overlay to ensure text readability */
.products::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(248, 249, 250, 0.85);
    z-index: 1;
}

.products .container {
    position: relative;
    z-index: 2;
}

.products h2 {
    text-align: center;
    font-size: 2.5rem;
    margin-bottom: 3rem;
    color: #0066cc;
}

.products-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 2rem;
}

.product-card {
    background: white;
    border-radius: 15px;
    overflow: hidden;
    box-shadow: 0 5px 20px rgba(0,0,0,0.1);
    transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.product-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 15px 40px rgba(0,0,0,0.15);
}

.product-image {
    height: 200px;
    background: linear-gradient(45deg, #0066cc, #004499);
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 1.2rem;
    font-weight: bold;
}

.product-content {
    padding: 1.5rem;
}

.product-card h3 {
    font-size: 1.3rem;
    margin-bottom: 0.5rem;
    color: #333;
}

.product-card p {
    color: #666;
    margin-bottom: 1rem;
}

.product-link {
    color: #0066cc;
    text-decoration: none;
    font-weight: 500;
    transition: color 0.3s ease;
}

.product-link:hover {
    color: #004499;
}

.product-specs {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 0.5rem;
    font-size: 0.9rem;
}

.spec {
    background: #f8f9fa;
    padding: 0.25rem 0.5rem;
    border-radius: 15px;
    color: #666;
    font-weight: 500;
}

.price {
    color: #0066cc;
    font-weight: bold;
}

/* Testimonials Section */
.testimonials {
    padding: 5rem 0;
    background: #0066cc;
    color: white;
}

.testimonials h2 {
    text-align: center;
    font-size: 2.5rem;
    margin-bottom: 3rem;
    color: white;
}

.testimonials-slider {
    position: relative;
    max-width: 800px;
    margin: 0 auto;
}

.testimonial-slide {
    display: none;
    text-align: center;
    padding: 2rem;
}

.testimonial-slide.active {
    display: block;
}

.testimonial-content p {
    font-size: 1.2rem;
    font-style: italic;
    margin-bottom: 1.5rem;
    line-height: 1.8;
}

.testimonial-content strong {
    color: #ffcc00;
    font-size: 1rem;
}

.testimonial-navigation {
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 2rem;
    margin-top: 2rem;
}

.testimonial-prev,
.testimonial-next {
    background: rgba(255,255,255,0.2);
    border: none;
    color: white;
    width: 40px;
    height: 40px;
    border-radius: 50%;
    font-size: 1.5rem;
    cursor: pointer;
    transition: all 0.3s ease;
}

.testimonial-prev:hover,
.testimonial-next:hover {
    background: rgba(255,255,255,0.3);
}

.testimonial-dots {
    display: flex;
    gap: 0.5rem;
}

.testimonial-dot {
    width: 12px;
    height: 12px;
    border-radius: 50%;
    background: rgba(255,255,255,0.3);
    cursor: pointer;
    transition: all 0.3s ease;
}

.testimonial-dot.active {
    background: #ffcc00;
}

/* Contact Section */
.contact {
    padding: 5rem 0;
    background: #f8f9fa;
    text-align: center;
}

.contact h2 {
    font-size: 2.5rem;
    margin-bottom: 1rem;
    color: #0066cc;
}

.contact p {
    font-size: 1.2rem;
    margin-bottom: 3rem;
    color: #666;
}

.contact-info {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 2rem;
    max-width: 800px;
    margin: 0 auto;
}

.contact-item {
    background: white;
    padding: 2rem;
    border-radius: 15px;
    box-shadow: 0 5px 20px rgba(0,0,0,0.1);
}

.contact-item h3 {
    font-size: 1.3rem;
    margin-bottom: 1rem;
    color: #0066cc;
}

.contact-item p {
    font-size: 1.1rem;
    font-weight: bold;
    color: #333;
    margin: 0;
}

/* About Section */
.about {
    padding: 5rem 0;
    background: white;
}

.about-content {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 3rem;
    align-items: center;
}

.about h2 {
    font-size: 2.5rem;
    margin-bottom: 1.5rem;
    color: #0066cc;
}

.about p {
    font-size: 1.1rem;
    margin-bottom: 1rem;
    color: #555;
}

.about-image {
    height: 400px;
    background: linear-gradient(45deg, #0066cc, #004499);
    border-radius: 15px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 1.2rem;
    font-weight: bold;
}

/* Footer */
.site-footer {
    background: #333;
    color: white;
    padding: 3rem 0 1rem;
}

.footer-content {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 2rem;
    margin-bottom: 2rem;
}

.footer-section h3 {
    margin-bottom: 1rem;
    color: #ffcc00;
}

.footer-section a {
    color: #ccc;
    text-decoration: none;
    transition: color 0.3s ease;
}

.footer-section a:hover {
    color: #ffcc00;
}

.footer-bottom {
    text-align: center;
    padding-top: 2rem;
    border-top: 1px solid #555;
    color: #ccc;
}



/* Animation classes */
.feature-card,
.product-card {
    opacity: 0;
    transform: translateY(30px);
    transition: all 0.6s ease;
}

.feature-card.animate-in,
.product-card.animate-in {
    opacity: 1;
    transform: translateY(0);
}

/* Back to top button */
.back-to-top {
    position: fixed;
    bottom: 30px;
    right: 30px;
    background: #0066cc;
    color: white;
    width: 50px;
    height: 50px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    text-decoration: none;
    font-size: 1.5rem;
    box-shadow: 0 4px 15px rgba(0,0,0,0.2);
    transition: all 0.3s ease;
    z-index: 1000;
}

.back-to-top:hover {
    background: #004499;
    transform: translateY(-3px);
}

/* Enhanced product cards */
.product-card {
    position: relative;
    overflow: hidden;
}

.product-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
    transition: left 0.5s;
}

.product-card:hover::before {
    left: 100%;
}

/* Modern Responsive Design */
@media (max-width: 768px) {
    .mobile-menu-toggle {
        display: flex;
    }

    .main-nav {
        position: fixed;
        top: 100%;
        left: 0;
        width: 100%;
        /* Match the header gradient for mobile menu */
        background: linear-gradient(
            180deg,
            rgba(0, 120, 200, 0.98) 0%,
            rgba(0, 90, 160, 0.99) 50%,
            rgba(0, 40, 80, 0.99) 100%
        );
        backdrop-filter: blur(20px);
        -webkit-backdrop-filter: blur(20px);
        transform: translateY(-100%);
        opacity: 0;
        visibility: hidden;
        transition: all 0.3s ease;
        z-index: 999;
        box-shadow:
            0 4px 20px rgba(0, 0, 0, 0.25),
            0 8px 32px rgba(0, 0, 0, 0.15);
    }

    .main-nav.active {
        transform: translateY(0);
        opacity: 1;
        visibility: visible;
    }

    .main-nav .nav-menu {
        flex-direction: column;
        gap: 0;
        padding: 1rem 0;
        margin: 0;
    }

    .main-nav .nav-menu li {
        width: 100%;
        border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    }

    .main-nav .nav-menu li:last-child {
        border-bottom: none;
    }

    .main-nav .nav-menu a {
        display: block;
        padding: 1rem 2rem;
        width: 100%;
        font-size: 1rem;
        border-radius: 0;
        transition: background-color 0.2s ease;
    }

    .main-nav .nav-menu a:hover {
        background: rgba(255, 255, 255, 0.1);
        transform: none;
        box-shadow: none;
    }

    .hero {
        min-height: 80vh;
        padding: 4rem 0;
    }

    .hero-buttons {
        flex-direction: column;
        align-items: center;
        gap: 1rem;
    }

    .cta-button {
        width: 100%;
        max-width: 280px;
        justify-content: center;
    }

    .hero-stats {
        gap: 1.5rem;
    }

    .stat {
        min-width: 120px;
        padding: 1.5rem 1rem;
    }

    .features-grid {
        grid-template-columns: 1fr;
        gap: 2rem;
    }

    .feature-card {
        padding: 2rem 1.5rem;
    }

    .products-grid {
        grid-template-columns: 1fr;
    }

    .about-content {
        grid-template-columns: 1fr;
        gap: 2rem;
    }

    .contact-info {
        flex-direction: column;
        gap: 2rem;
    }
}

@media (max-width: 480px) {
    .container {
        padding: 0 15px;
    }

    .hero h1 {
        font-size: 2rem;
    }

    .hero p {
        font-size: 1rem;
    }

    .hero-stats {
        flex-direction: column;
        align-items: center;
        gap: 1rem;
    }

    .stat {
        width: 100%;
        max-width: 200px;
    }

    .features h2,
    .products h2 {
        font-size: 1.8rem;
    }
}

/* Additional Modern Enhancements */
.loading-spinner {
    display: inline-block;
    width: 16px;
    height: 16px;
    border: 2px solid rgba(255, 255, 255, 0.3);
    border-radius: 50%;
    border-top-color: var(--white);
    animation: spin 1s ease-in-out infinite;
}

@keyframes spin {
    to {
        transform: rotate(360deg);
    }
}

.animate-in {
    animation: fadeInUp 0.8s ease forwards;
}

.hover-active {
    transform: translateY(-2px);
}

/* Particle animation for hero background */
@keyframes float {
    0%, 100% {
        transform: translateY(0px) rotate(0deg);
    }
    33% {
        transform: translateY(-10px) rotate(120deg);
    }
    66% {
        transform: translateY(5px) rotate(240deg);
    }
}

/* Smooth scrolling enhancement */
html {
    scroll-behavior: smooth;
}

/* Focus styles for accessibility */
.cta-button:focus,
.main-nav a:focus {
    outline: 2px solid var(--accent-color);
    outline-offset: 2px;
}

/* Modern Button Icons */
.cta-button svg {
    transition: transform var(--transition-fast);
}

.cta-button:hover svg {
    transform: translateX(3px);
}

.cta-button.secondary svg {
    stroke: currentColor;
}

/* Enhanced Product Cards */
.product-card {
    background: var(--white);
    border-radius: 20px;
    overflow: hidden;
    box-shadow: var(--shadow-light);
    transition: all var(--transition-medium);
    border: 1px solid rgba(0, 102, 204, 0.1);
}

.product-card:hover {
    transform: translateY(-8px) scale(1.02);
    box-shadow: var(--shadow-heavy);
}

.product-image {
    height: 200px;
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--white);
    font-weight: 600;
    position: relative;
    overflow: hidden;
}

.product-image::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(45deg, transparent 30%, rgba(255, 255, 255, 0.1) 50%, transparent 70%);
    transform: translateX(-100%);
    transition: transform var(--transition-medium);
}

.product-card:hover .product-image::before {
    transform: translateX(100%);
}

.product-content {
    padding: 2rem;
}

.product-content h3 {
    font-family: 'Poppins', sans-serif;
    font-weight: 600;
    color: var(--text-dark);
    margin-bottom: 1rem;
}

.product-link {
    color: var(--primary-color);
    text-decoration: none;
    font-weight: 600;
    transition: all var(--transition-fast);
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
}

.product-link:hover {
    color: var(--secondary-color);
    transform: translateX(3px);
}

/* Enhanced Testimonials */
.testimonials {
    padding: 8rem 0;
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-dark) 100%);
    color: var(--white);
    position: relative;
    overflow: hidden;
}

.testimonials::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: radial-gradient(circle at 70% 30%, rgba(255, 204, 0, 0.1) 0%, transparent 50%);
    pointer-events: none;
}

.testimonials h2 {
    text-align: center;
    font-family: 'Poppins', sans-serif;
    font-size: clamp(2rem, 4vw, 3rem);
    font-weight: 700;
    margin-bottom: 4rem;
    color: var(--white);
}

.testimonial-slide {
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(15px);
    -webkit-backdrop-filter: blur(15px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 20px;
    padding: 3rem;
    text-align: center;
    transition: all var(--transition-medium);
}

.testimonial-slide:hover {
    transform: translateY(-5px);
    background: rgba(255, 255, 255, 0.15);
}

/* Modern About Section */
.about {
    padding: 8rem 0;
    background: var(--white);
}

.about-content {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 4rem;
    align-items: center;
}

.about h2 {
    font-family: 'Poppins', sans-serif;
    font-size: clamp(2rem, 4vw, 2.5rem);
    font-weight: 700;
    color: var(--text-dark);
    margin-bottom: 2rem;
}

.about p {
    color: var(--text-light);
    line-height: 1.8;
    margin-bottom: 1.5rem;
}

.about-image {
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    color: var(--white);
    padding: 4rem 2rem;
    border-radius: 20px;
    text-align: center;
    font-weight: 600;
    font-size: 1.2rem;
}

/* Modern Contact Section */
.contact {
    padding: 8rem 0;
    background: linear-gradient(135deg, var(--light-bg) 0%, var(--white) 100%);
}

.contact h2 {
    text-align: center;
    font-family: 'Poppins', sans-serif;
    font-size: clamp(2rem, 4vw, 2.5rem);
    font-weight: 700;
    color: var(--text-dark);
    margin-bottom: 1rem;
}

.contact > .container > p {
    text-align: center;
    color: var(--text-light);
    font-size: 1.1rem;
    margin-bottom: 4rem;
    max-width: 600px;
    margin-left: auto;
    margin-right: auto;
}

.contact-info {
    display: flex;
    justify-content: center;
    gap: 3rem;
    flex-wrap: wrap;
}

.contact-item {
    background: var(--white);
    padding: 2.5rem 2rem;
    border-radius: 20px;
    text-align: center;
    box-shadow: var(--shadow-light);
    border: 1px solid rgba(0, 102, 204, 0.1);
    transition: all var(--transition-medium);
    min-width: 250px;
}

.contact-item:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-medium);
}

.contact-item h3 {
    font-family: 'Poppins', sans-serif;
    font-weight: 600;
    color: var(--text-dark);
    margin-bottom: 1rem;
    font-size: 1.2rem;
}

.contact-item p {
    color: var(--text-light);
    font-weight: 500;
}

/* Print styles */
@media print {
    .site-header,
    .mobile-menu-toggle,
    .back-to-top {
        display: none;
    }

    body {
        padding-top: 0;
    }
}

    .back-to-top {
        bottom: 20px;
        right: 20px;
        width: 45px;
        height: 45px;
    }
}

@media (max-width: 480px) {
    .hero {
        padding: 4rem 0;
    }

    .hero h1 {
        font-size: 2rem;
    }

    .container {
        padding: 0 15px;
    }

    .features,
    .products,
    .about {
        padding: 3rem 0;
    }

    .features h2,
    .products h2,
    .about h2 {
        font-size: 2rem;
    }
}
