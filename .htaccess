# BEGIN WordPress
# The directives (lines) between "BEGIN WordPress" and "END WordPress" are
# dynamically generated, and should only be modified via WordPress filters.
# Any changes to the directives between these markers will be overwritten.

# END WordPress

# Security Headers
<IfModule mod_headers.c>
    Header always set X-Content-Type-Options nosniff
    Header always set X-Frame-Options DENY
    Header always set X-XSS-Protection "1; mode=block"
    Header always set Referrer-Policy "strict-origin-when-cross-origin"
</IfModule>

# ===== DISABLE ALL CACHING FOR DEVELOPMENT =====
# Disable browser caching for CSS, JS, and HTML files
<IfModule mod_headers.c>
    # Disable caching for CSS files
    <FilesMatch "\.(css)$">
        Header set Cache-Control "no-cache, no-store, must-revalidate"
        Header set Pragma "no-cache"
        Header set Expires "0"
    </FilesMatch>

    # Disable caching for JavaScript files
    <FilesMatch "\.(js)$">
        Header set Cache-Control "no-cache, no-store, must-revalidate"
        Header set Pragma "no-cache"
        Header set Expires "0"
    </FilesMatch>

    # Disable caching for HTML files
    <FilesMatch "\.(html|htm|php)$">
        Header set Cache-Control "no-cache, no-store, must-revalidate"
        Header set Pragma "no-cache"
        Header set Expires "0"
    </FilesMatch>

    # Add cache-busting headers for all requests
    Header set Cache-Control "no-cache, no-store, must-revalidate"
    Header set Pragma "no-cache"
    Header set Expires "0"
</IfModule>

# Disable ETags
<IfModule mod_headers.c>
    Header unset ETag
</IfModule>
FileETag None

# Force revalidation of cached content
<IfModule mod_expires.c>
    ExpiresActive Off
</IfModule>

# Disable compression for development (can interfere with debugging)
<IfModule mod_deflate.c>
    SetEnv no-gzip 1
</IfModule>
# ===== END CACHING DISABLE =====
